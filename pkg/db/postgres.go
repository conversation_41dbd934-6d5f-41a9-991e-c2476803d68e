package db

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"time"

	"github.com/mneme/db-tools/pkg/config"
)

// PostgresBackup creates a backup of a PostgreSQL database
func PostgresBackup(dbConfig config.BackupDatabaseConfig) (string, error) {
	log.Printf("Starting PostgreSQL backup for database %s", dbConfig.Database)

	// Create a timestamp for the backup file
	timestamp := time.Now().Format("20060102150405")
	backupFile := fmt.Sprintf("%s_%s.dump", dbConfig.Database, timestamp)

	// Create the pg_dump command
	cmd := exec.Command("pg_dump",
		"-h", dbConfig.Host,
		"-p", fmt.Sprintf("%d", dbConfig.Port),
		"-U", dbConfig.Username,
		"-d", dbConfig.Database,
		"-f", backupFile,
		"-Fc") // Use custom format (compressed)
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))

	// Execute the backup command
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("failed to create backup: %v\nOutput: %s", err, string(output))
	}

	log.Printf("PostgreSQL backup completed for database %s: %s", dbConfig.Database, backupFile)
	return backupFile, nil
}

// CheckPostgresConnection checks if the PostgreSQL server is accessible
func CheckPostgresConnection(dbConfig config.RestoreDatabaseConfig) error {
	log.Printf("Checking PostgreSQL connection to %s:%d", dbConfig.Host, dbConfig.Port)

	// Try to use root user for connection check if available
	rootUsername := os.Getenv("POSTGRES_ROOT_USERNAME")
	rootPassword := os.Getenv("POSTGRES_ROOT_PASSWORD")

	// If root credentials are not provided, fall back to the regular user
	username := dbConfig.Username
	password := dbConfig.Password

	if rootUsername != "" && rootPassword != "" {
		log.Printf("Using root credentials for PostgreSQL connection check")
		username = rootUsername
		password = rootPassword
	} else {
		log.Printf("Using regular credentials for PostgreSQL connection check")
	}

	// Create a simple connection check command
	checkCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", fmt.Sprintf("%d", dbConfig.Port),
		"-U", username,
		"-d", "postgres", // Connect to the default postgres database
		"-c", "SELECT 1")
	checkCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", password))
	checkCmd.Stderr = os.Stderr

	if err := checkCmd.Run(); err != nil {
		return fmt.Errorf("failed to connect to PostgreSQL server: %v", err)
	}

	log.Printf("PostgreSQL connection to %s:%d successful", dbConfig.Host, dbConfig.Port)
	return nil
}

// PostgresRestore restores a PostgreSQL database from a backup file
func PostgresRestore(dbConfig config.RestoreDatabaseConfig, backupFile string) error {
	log.Printf("Restoring PostgreSQL database %s from backup %s", dbConfig.Database, backupFile)

	// Try to use root user for database creation if available
	rootUsername := os.Getenv("POSTGRES_ROOT_USERNAME")
	rootPassword := os.Getenv("POSTGRES_ROOT_PASSWORD")

	// If root credentials are not provided, fall back to the regular user
	username := dbConfig.Username
	password := dbConfig.Password

	if rootUsername != "" && rootPassword != "" {
		log.Printf("Using root credentials to create database %s", dbConfig.Database)
		username = rootUsername
		password = rootPassword
	} else {
		log.Printf("Using regular credentials to create database %s", dbConfig.Database)
	}

	// First, create the database if it doesn't exist
	createDBCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", fmt.Sprintf("%d", dbConfig.Port),
		"-U", username,
		"-d", "postgres", // Connect to the default postgres database for admin operations
		"-c", fmt.Sprintf("CREATE DATABASE \"%s\" WITH OWNER = \"%s\"", dbConfig.Database, dbConfig.Username))
	createDBCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", password))
	createDBCmd.Stdout = os.Stdout
	createDBCmd.Stderr = os.Stderr

	log.Printf("Creating database %s if it doesn't exist", dbConfig.Database)
	// Ignore errors from create database as it might already exist
	_ = createDBCmd.Run()

	// Get file size for progress estimation
	fileInfo, err := os.Stat(backupFile)
	if err != nil {
		log.Printf("Warning: Could not get backup file size: %v", err)
	} else {
		log.Printf("Starting PostgreSQL restore of %d bytes", fileInfo.Size())
	}

	// Create the pg_restore command with additional flags to handle RDS backups
	cmd := exec.Command("pg_restore",
		"-h", dbConfig.Host,
		"-p", fmt.Sprintf("%d", dbConfig.Port),
		"-U", dbConfig.Username,
		"-d", dbConfig.Database,
		"-c",         // Clean (drop) database objects before recreating
		"-v",         // Verbose mode
		"--no-owner", // Skip restoration of object ownership
		"--no-acl",   // Skip restoration of access privileges (GRANT/REVOKE)
		backupFile)
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	log.Printf("Using --no-owner and --no-acl flags to skip ownership and permission statements")

	// Start a timer to track progress
	startTime := time.Now()
	log.Printf("PostgreSQL restore started at %s", startTime.Format(time.RFC3339))

	// Execute the restore command
	err = cmd.Run()

	// Calculate and log the time taken
	duration := time.Since(startTime)

	if err != nil {
		// pg_restore often returns non-zero exit code even when it successfully restores most objects
		// This is especially common when restoring RDS backups to non-RDS PostgreSQL
		log.Printf("Warning: pg_restore completed with errors: %v", err)
		log.Printf("This is often normal when restoring RDS backups due to role/permission differences")
		log.Printf("The database may still be usable despite these errors")
		log.Printf("PostgreSQL database %s restored with warnings in %s", dbConfig.Database, duration)

		// Continue execution instead of returning an error
		// If there are critical errors, they will be apparent when trying to use the database
	} else {
		log.Printf("PostgreSQL database %s restored successfully in %s", dbConfig.Database, duration)
	}

	return nil
}

// PostgresDump creates a dump of a PostgreSQL database
func PostgresDump(dbConfig config.RestoreDatabaseConfig) (string, error) {
	log.Printf("Creating PostgreSQL dump for database %s", dbConfig.Database)

	// Create a temporary file to store the dump
	tempFile, err := os.CreateTemp("", fmt.Sprintf("%s-sanitized-*.dump", dbConfig.Database))
	if err != nil {
		return "", fmt.Errorf("failed to create temporary file for sanitized dump: %v", err)
	}
	tempFile.Close() // Close the file so pg_dump can write to it

	// Create the pg_dump command
	cmd := exec.Command("pg_dump",
		"-h", dbConfig.Host,
		"-p", fmt.Sprintf("%d", dbConfig.Port),
		"-U", dbConfig.Username,
		"-d", dbConfig.Database,
		"-f", tempFile.Name(),
		"-Fc") // Use custom format (compressed)
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// Start a timer to track progress
	startTime := time.Now()
	log.Printf("PostgreSQL dump started at %s", startTime.Format(time.RFC3339))

	// Execute the dump command
	if err := cmd.Run(); err != nil {
		os.Remove(tempFile.Name()) // Clean up the temp file if dump fails
		return "", fmt.Errorf("failed to create dump: %v", err)
	}

	// Get file size and calculate duration
	fileInfo, err := os.Stat(tempFile.Name())
	if err != nil {
		log.Printf("Warning: Could not get dump file size: %v", err)
	} else {
		duration := time.Since(startTime)
		log.Printf("PostgreSQL dump created successfully: %s (%d bytes) in %s",
			tempFile.Name(), fileInfo.Size(), duration)
		return tempFile.Name(), nil
	}

	duration := time.Since(startTime)
	log.Printf("PostgreSQL dump created successfully: %s in %s", tempFile.Name(), duration)
	return tempFile.Name(), nil
}
