apiVersion: batch/v1
kind: CronJob
metadata:
  name: mneme-hourly
spec:
  schedule: "0 * * * *"  # Run every hour
  jobTemplate:
    spec:
      ttlSecondsAfterFinished: 1800  # Delete succeeded jobs after 30 minutes
      template:
        spec:
          serviceAccountName: mneme
          containers:
          - name: mneme
            image: mneme
            imagePullPolicy: IfNotPresent
            command: ["/bin/sh", "-c"]
            args: ["/usr/local/bin/mneme $SECRETS_MANAGER_ARN"]
            env:
            - name: AWS_REGION
              value: "ca-central-1"
            - name: SECRETS_MANAGER_ARN
              valueFrom:
                secretKeyRef:
                  name: mneme-secrets
                  key: hourly-secret-arn
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          restartPolicy: OnFailure 